// Import required Garmin Connect IQ SDK modules for watch face view functionality
using Toybox.WatchUi as Ui;                    // User interface framework and watch face base classes
using Toybox.Graphics as Graphics;             // Graphics drawing and rendering capabilities
using Toybox.System as Sys;                    // System services and device information
using Toybox.Application as App;               // Application context and property access
using Toybox.ActivityMonitor as ActivityMonitor; // Activity and fitness data access

using Toybox.Math;                             // Mathematical functions and calculations

import Toybox.Lang;
import Toybox.WatchUi;

// Logger utility is available as a class (no using statement needed)

/**
 * Integer formatting constant for consistent number display throughout the watch face.
 * Used for displaying numeric values without decimal places.
 */
const INTEGER_FORMAT = "%d";

/**
 * Global color variables for theme management.
 * These colors are set based on user theme selection and device capabilities.
 * Updated in updateThemeColours() method when settings change.
 */
var gThemeColour;           // Primary theme color (blue, red, green, etc.)
var gMonoLightColour;       // Light monochrome color (white on dark themes, black on light themes)
var gMonoDarkColour;        // Dark monochrome color (light gray on dark themes, dark gray on light themes)
var gBackgroundColour;      // Background color (black for dark themes, white for light themes)
var gMeterBackgroundColour; // Color for unfilled meter segments
var gHoursColour;           // Color for hour digits (can be overridden by user)
var gMinutesColour;         // Color for minute digits (can be overridden by user)

/**
 * Global font variables for consistent typography.
 * Fonts are loaded based on feature usage to optimize memory consumption.
 */
var gNormalFont;            // Normal text font (varies based on city feature usage)
var gIconsFont;             // Icon font for symbols and indicators

/**
 * Screen size adaptation constants.
 * These constants help adapt the UI for different screen sizes and pixel densities.
 */
const SCREEN_MULTIPLIER = (Sys.getDeviceSettings().screenWidth < 360) ? 1 : 2;

/**
 * Battery meter drawing constants.
 * These constants define the visual appearance of battery indicators.
 */
//const BATTERY_LINE_WIDTH = 2;                    // Stroke width for battery outline (hardcoded in drawing)
const BATTERY_HEAD_HEIGHT = 4 * SCREEN_MULTIPLIER; // Height of battery terminal/head
const BATTERY_MARGIN = SCREEN_MULTIPLIER;           // Margin around battery fill area

/**
 * Battery level thresholds for color coding.
 * These constants define when to show warning colors for low battery.
 */
//const BATTERY_LEVEL_LOW = 20;        // Yellow warning threshold (20%)
//const BATTERY_LEVEL_CRITICAL = 10;   // Red critical threshold (10%)

/**
 * Draws a battery meter indicator with level-based color coding.
 *
 * This function renders a battery icon with:
 * - Rounded rectangle body with stroke outline
 * - Small rectangular terminal/head on the right side
 * - Proportional fill based on current battery level
 * - Color coding: red (≤10%), yellow (≤20%), theme color (>20%)
 *
 * Visual Design:
 * - Centered positioning based on x,y coordinates
 * - Adaptive sizing based on screen multiplier
 * - Consistent with system battery display behavior
 *
 * @param dc Graphics drawing context
 * @param x X coordinate of battery center point
 * @param y Y coordinate of battery center point
 * @param width Outer width of battery body
 * @param height Outer height of battery body
 */
function drawBatteryMeter(dc, x, y, width, height) {
	// Set up drawing properties for battery outline
	dc.setColor(gThemeColour, Graphics.COLOR_TRANSPARENT);
	dc.setPenWidth(/* BATTERY_LINE_WIDTH */ 2);

	// Draw battery body outline
	// Note: drawRoundedRectangle's x,y parameters specify top-left corner of stroke middle
	// Bottom-right corner of stroke middle will be at (x + width - 1, y + height - 1)
	dc.drawRoundedRectangle(
		x - (width / 2) + /* (BATTERY_LINE_WIDTH / 2) */ 1,    // Left edge adjustment
		y - (height / 2) + /* (BATTERY_LINE_WIDTH / 2) */ 1,   // Top edge adjustment
		width - /* BATTERY_LINE_WIDTH + 1 */ 1,                // Width adjustment for stroke
		height - /* BATTERY_LINE_WIDTH + 1 */ 1,               // Height adjustment for stroke
		/* BATTERY_CORNER_RADIUS */ 2 * SCREEN_MULTIPLIER);    // Corner radius scaled for screen

	// Draw battery terminal/head (small rectangle on right side)
	// fillRectangle() coordinates work as expected (top-left corner)
	dc.fillRectangle(
		x + (width / 2) + BATTERY_MARGIN,    // Position to right of body with margin
		y - (BATTERY_HEAD_HEIGHT / 2),       // Vertically centered
		/* BATTERY_HEAD_WIDTH */ 2,          // Fixed width terminal
		BATTERY_HEAD_HEIGHT);                // Height scaled for screen

	// Get current battery level and apply consistent rounding
	// Issue #8: Battery returned as float, use floor() to match native behavior
	// Must match calculation in getValueForFieldType() for consistency
	var batteryLevel = Math.floor(Sys.getSystemStats().battery);

	// Determine fill color based on battery level thresholds
	var fillColour;
	if (batteryLevel <= /* BATTERY_LEVEL_CRITICAL */ 10) {
		fillColour = Graphics.COLOR_RED;      // Critical level: red warning
	} else if (batteryLevel <= /* BATTERY_LEVEL_LOW */ 20) {
		fillColour = Graphics.COLOR_YELLOW;   // Low level: yellow warning
	} else {
		fillColour = gThemeColour;            // Normal level: theme color
	}

	// Draw battery fill proportional to current level
	dc.setColor(fillColour, Graphics.COLOR_TRANSPARENT);

	var lineWidthPlusMargin = (/* BATTERY_LINE_WIDTH */ 2 + BATTERY_MARGIN);
	var fillWidth = width - (2 * lineWidthPlusMargin);
	dc.fillRectangle(
		x - (width / 2) + lineWidthPlusMargin,                    // Left edge inside stroke and margin
		y - (height / 2) + lineWidthPlusMargin,                   // Top edge inside stroke and margin
		Math.ceil(fillWidth * (batteryLevel / 100)),              // Width proportional to battery level
		// Height calculation with screen-specific adjustment
		// Pixel alignment differs on screens ≥360px wide (not just AMOLED color depth)
		// Add 1px height on larger screens for proper visual alignment
		height - (2 * lineWidthPlusMargin) + (SCREEN_MULTIPLIER - 1));
}

/**
 * Type definition for goal meter values and validation state.
 * Used to pass goal data between the view and goal meter components.
 */
typedef GoalValues as {
	:current as Number,     // Current progress value (steps taken, calories burned, etc.)
	:max as Number,         // Maximum/target value for the goal
	:isValid as Boolean     // Whether the goal data is valid and should be displayed
};

/**
 * MadreView class serves as the main watch face view controller.
 *
 * This class extends WatchFace and manages:
 * - Watch face rendering and layout management
 * - Power mode transitions (sleep/wake, burn-in protection)
 * - Theme and color management
 * - Component coordination and data flow
 * - Settings change handling and propagation
 * - Performance optimization through drawable caching
 *
 * Key Features:
 * - Adaptive layout for different screen sizes and shapes
 * - Burn-in protection for AMOLED displays
 * - Per-second updates for supported devices
 * - Efficient drawable caching to avoid expensive lookups
 * - Theme-based color management with user overrides
 * - Goal meter integration with activity monitoring
 *
 * Power Management:
 * - Sleep mode detection and UI adaptation
 * - Burn-in protection activation/deactivation
 * - Conditional seconds display based on device capabilities
 * - Memory optimization during low-power states
 */
class MadreView extends Ui.WatchFace {
	// Power and display state management
	private var mIsSleeping = false;                                    // Current sleep state of the watch
	private var mIsBurnInProtection = false;                           // Is burn-in protection currently active?
	private var mBurnInProtectionChangedSinceLastDraw = false;         // Flag for burn-in protection state changes
	private var mSettingsChangedSinceLastDraw = true;                  // Flag for settings changes requiring full redraw

	// Core component references (optimized for frequent access)
	private var mTime;                                                  // Direct reference to time display component
	var mDataFields;                                                    // Direct reference to data fields component

	// Drawable cache for performance optimization
	// Caches references immediately after layout to avoid expensive findDrawableById() calls in onUpdate()
	private var mDrawables as Dictionary<Symbol, Drawable> = {};

	// Device capability detection
	// Note: Not all watches supporting SDK 2.3.0 support per-second updates (e.g., 735xt)
	private const PER_SECOND_UPDATES_SUPPORTED = Ui.WatchFace has :onPartialUpdate;

	/**
	 * Theme enumeration (commented out but values used directly in arrays).
	 * These correspond to user-selectable themes in watch face settings.
	 */
	// private enum /* THEMES */ {
	// 	THEME_BLUE_DARK,           // 0: Blue theme with dark background
	// 	THEME_PINK_DARK,           // 1: Pink theme with dark background
	// 	THEME_GREEN_DARK,          // 2: Green theme with dark background
	// 	THEME_MONO_LIGHT,          // 3: Monochrome theme with light background
	// 	THEME_CORNFLOWER_BLUE_DARK,// 4: Cornflower blue theme with dark background
	// 	THEME_LEMON_CREAM_DARK,    // 5: Lemon cream theme with dark background
	// 	THEME_DAYGLO_ORANGE_DARK,  // 6: Dayglo orange theme with dark background
	// 	THEME_RED_DARK,            // 7: Red theme with dark background
	// 	THEME_MONO_DARK,           // 8: Monochrome theme with dark background
	// 	THEME_BLUE_LIGHT,          // 9: Blue theme with light background
	// 	THEME_GREEN_LIGHT,         // 10: Green theme with light background
	// 	THEME_RED_LIGHT,           // 11: Red theme with light background
	// 	THEME_VIVID_YELLOW_DARK,   // 12: Vivid yellow theme with dark background
	// 	THEME_DAYGLO_ORANGE_LIGHT, // 13: Dayglo orange theme with light background
	// 	THEME_CORN_YELLOW_DARK     // 14: Corn yellow theme with dark background
	// }

	/**
	 * Color override enumeration (commented out but values used directly).
	 * These allow users to override hours/minutes colors independently of theme.
	 */
	// private enum /* COLOUR_OVERRIDES */ {
	// 	FROM_THEME = -1,      // Use theme color (default)
	// 	MONO_HIGHLIGHT = -2,  // Use monochrome highlight color
	// 	MONO = -3             // Use monochrome color
	// }

	/**
	 * Constructor for the watch face view.
	 * Initializes the base WatchFace class and prepares for layout and rendering.
	 */
	function initialize() {
		// Debug output using standard System.println (appears in Connect IQ Simulator Console)
		Sys.println("[INFO] MadreView: === MADRE WATCH FACE INITIALIZING ===");

		WatchFace.initialize();

		// Sys.println("[DEBUG] MadreView: WatchFace base class initialized");
	}

	/**
	 * Loads resources and sets up the initial layout.
	 *
	 * This method is called by the system when the watch face is first displayed
	 * and handles:
	 * - Loading essential fonts (icons font is always needed)
	 * - Setting up the main watch face layout
	 * - Caching drawable references for performance optimization
	 *
	 * @param dc Graphics drawing context (used for layout setup)
	 */
	function onLayout(dc) {
		// Load icon font (always required for various UI elements)
		gIconsFont = Ui.loadResource(Rez.Fonts.IconsFont);

		// Set up the main watch face layout from resources
		setLayout(Rez.Layouts.WatchFace(dc));

		// Cache drawable references to avoid expensive lookups during updates
		cacheDrawables();
	}

	/**
	 * Caches references to drawable components for performance optimization.
	 *
	 * This method stores references to frequently accessed drawables to avoid expensive
	 * findDrawableById() calls during onUpdate() cycles. Performance testing showed
	 * significant improvements, especially for time display components.
	 *
	 * Performance Benefits:
	 * - Eliminates repeated string-based drawable lookups
	 * - Saves nearly 5ms per update cycle for time components
	 * - Reduces CPU usage during frequent updates (per-second mode)
	 *
	 * Optimization Strategy:
	 * - Most frequently accessed components (Time, DataFields) get direct references
	 * - Less frequently accessed components use mDrawables dictionary
	 * - Direct references are slightly faster than dictionary lookups
	 */
	function cacheDrawables() {
		// Cache goal meter references (used in updateGoalMeters)
		mDrawables[:LeftGoalMeter] = View.findDrawableById("LeftGoalMeter");
		mDrawables[:RightGoalMeter] = View.findDrawableById("RightGoalMeter");

		// Cache data area and indicators (used in various update cycles)
		mDrawables[:DataArea] = View.findDrawableById("DataArea");
		mDrawables[:Indicators] = View.findDrawableById("Indicators");

		// Direct reference optimization for time display
		// Performance critical: saves nearly 5ms per update cycle
		// Slightly faster than mDrawables dictionary lookup
		//mDrawables[:Time] = View.findDrawableById("Time");  // Original approach
		mTime = View.findDrawableById("Time");

		// Direct reference optimization for data fields
		// Used frequently in both full and partial updates
		//mDrawables[:DataFields] = View.findDrawableById("DataFields");  // Original approach
		mDataFields = View.findDrawableById("DataFields");

		// Cache move bar reference (used in seconds display management)
		mDrawables[:MoveBar] = View.findDrawableById("MoveBar");

		// Apply initial seconds display setting
		// Requires mTime and mDrawables[:MoveBar] to be cached first
		setHideSeconds(getPropertyValue("HideSeconds"));
	}

	/*
	// Called when this View is brought to the foreground. Restore
	// the state of this View and prepare it to be shown. This includes
	// loading resources into memory.
	function onShow() {
	}
	*/

	/**
	 * Handles settings changes from the user.
	 *
	 * This method sets a flag to respond to settings changes on the next full draw (onUpdate())
	 * because the watch may be in 1Hz (low power) mode where immediate full screen updates
	 * are not possible. This behavior is observed on real hardware but not in the simulator.
	 *
	 * Settings Processing:
	 * 1. Set flag for deferred processing during next onUpdate()
	 * 2. Update font selection based on feature usage
	 * 3. Apply theme color changes
	 * 4. Update hours/minutes color overrides
	 * 5. Check for new background web request requirements
	 *
	 * Performance Note:
	 * Ui.requestUpdate() does not appear to work reliably in 1Hz mode on real hardware,
	 * so settings changes are deferred until the next natural update cycle.
	 */
	function onSettingsChanged() {
		// Set flag for deferred processing (handles low power mode limitations)
		mSettingsChangedSinceLastDraw = true;

		// Update font selection based on city feature usage (memory optimization)
		updateNormalFont();

		// Apply theme changes: set color properties that have no user-facing settings
		updateThemeColours();

		// Update hours/minutes color overrides after theme colors are established
		updateHoursMinutesColours();

		// Check for new background web request requirements
		// checkPendingWebRequests() can be excluded at compile time to save memory
		if (MadreApp has :checkPendingWebRequests) {
			App.getApp().checkPendingWebRequests();
		}
	}

	/**
	 * Updates the normal font selection based on city feature usage.
	 *
	 * Memory optimization strategy that loads different font variants based on
	 * whether the city local time feature is being used.
	 *
	 * Font Selection Logic:
	 * - If city is configured: Load NormalFontCities (supports international characters)
	 * - If no city configured: Load NormalFont (standard character set)
	 *
	 * Memory Impact:
	 * - NormalFontCities: ~15-20KB (extended character sets for international cities)
	 * - NormalFont: ~8-12KB (basic character set for standard data fields)
	 * - Savings: ~7-8KB when city feature not used
	 *
	 * This optimization is particularly important on memory-constrained devices
	 * where every KB of saved memory improves overall performance.
	 */
	function updateNormalFont() {

		var city = getPropertyValue("LocalTimeInCity");

		// Issue #78: Setting with empty string value may cause property to be null
		gNormalFont = Ui.loadResource(((city != null) && (city.length() > 0)) ?
			Rez.Fonts.NormalFontCities : Rez.Fonts.NormalFont);
	}

	function updateThemeColours() {

		// #182 Protect against null or unexpected type e.g. String.
		var theme = App.getApp().getIntProperty("Theme", 0);

		// Theme-specific colours.
		gThemeColour = [
			Graphics.COLOR_BLUE,     // THEME_BLUE_DARK
			Graphics.COLOR_PINK,     // THEME_PINK_DARK
			Graphics.COLOR_GREEN,    // THEME_GREEN_DARK
			Graphics.COLOR_DK_GRAY,  // THEME_MONO_LIGHT
			0x55AAFF,                // THEME_CORNFLOWER_BLUE_DARK
			0xFFFFAA,                // THEME_LEMON_CREAM_DARK
			Graphics.COLOR_ORANGE,   // THEME_DAYGLO_ORANGE_DARK
			Graphics.COLOR_RED,      // THEME_RED_DARK
			Graphics.COLOR_WHITE,    // THEME_MONO_DARK
			Graphics.COLOR_DK_BLUE,  // THEME_BLUE_LIGHT
			Graphics.COLOR_DK_GREEN, // THEME_GREEN_LIGHT
			Graphics.COLOR_DK_RED,   // THEME_RED_LIGHT
			0xFFFF00,                // THEME_VIVID_YELLOW_DARK
			Graphics.COLOR_ORANGE,   // THEME_DAYGLO_ORANGE_LIGHT
			Graphics.COLOR_YELLOW    // THEME_CORN_YELLOW_DARK
		][theme];

		// Light/dark-specific colours.
		var lightFlags = [
			false, // THEME_BLUE_DARK
			false, // THEME_PINK_DARK
			false, // THEME_GREEN_DARK
			true,  // THEME_MONO_LIGHT
			false, // THEME_CORNFLOWER_BLUE_DARK
			false, // THEME_LEMON_CREAM_DARK
			false, // THEME_DAYGLO_ORANGE_DARK
			false, // THEME_RED_DARK
			false, // THEME_MONO_DARK
			true,  // THEME_BLUE_LIGHT
			true,  // THEME_GREEN_LIGHT
			true,  // THEME_RED_LIGHT
			false, // THEME_VIVID_YELLOW_DARK
			true,  // THEME_DAYGLO_ORANGE_LIGHT
			false, // THEME_CORN_YELLOW_DARK
		];

		// #124: fr45 cannot show grey.
		var isFr45 = (Sys.getDeviceSettings().screenWidth == 208);

		if (lightFlags[theme]) {
			gMonoLightColour = Graphics.COLOR_BLACK;
			gMonoDarkColour = isFr45 ? Graphics.COLOR_BLACK : Graphics.COLOR_DK_GRAY;

			gMeterBackgroundColour = isFr45 ? Graphics.COLOR_BLACK : Graphics.COLOR_LT_GRAY;
			gBackgroundColour = Graphics.COLOR_WHITE;
		} else {
			gMonoLightColour = Graphics.COLOR_WHITE;
			gMonoDarkColour = isFr45 ? Graphics.COLOR_WHITE : Graphics.COLOR_LT_GRAY;

			gMeterBackgroundColour = isFr45 ? Graphics.COLOR_WHITE : Graphics.COLOR_DK_GRAY;
			gBackgroundColour = Graphics.COLOR_BLACK;
		}
	}

	function updateHoursMinutesColours() {
		var overrideColours = [
			gThemeColour,     // FROM_THEME
			gMonoLightColour, // MONO_HIGHLIGHT
			gMonoDarkColour   // MONO
		];

		// #182 Protect against null or unexpected type e.g. String.
		// #182 Protect against invalid integer values (still crashing with getIntProperty()).
		var hco = App.getApp().getIntProperty("HoursColourOverride", 0);
		gHoursColour = overrideColours[(hco < 0 || hco > 2) ? 0 : hco];

		var mco = App.getApp().getIntProperty("MinutesColourOverride", 0);
		gMinutesColour = overrideColours[(mco < 0 || mco > 2) ? 0 : mco];
	}

	function onSettingsChangedSinceLastDraw() {
		if (!mIsBurnInProtection) {

			// Recreate background buffers for each meter, in case theme colour has changed.
			(mDrawables[:LeftGoalMeter] as GoalMeter).onSettingsChanged();
			(mDrawables[:RightGoalMeter] as GoalMeter).onSettingsChanged();

			(mDrawables[:MoveBar] as MoveBar).onSettingsChanged();

			mDataFields.onSettingsChanged();

			(mDrawables[:Indicators] as Indicators).onSettingsChanged();
		}

		// If watch does not support per-second updates, and watch is sleeping, do not show seconds immediately, as they will not
		// update. Instead, wait for next onExitSleep().
		if (PER_SECOND_UPDATES_SUPPORTED || !mIsSleeping) {
			setHideSeconds(getPropertyValue("HideSeconds"));
		}

		mSettingsChangedSinceLastDraw = false;
	}

	// Update the view
	function onUpdate(dc) {
		//Sys.println("onUpdate()");

		// If burn-in protection has changed, set layout appropriate to new burn-in protection state.
		// If turning on burn-in protection, free memory for regular watch face drawables by clearing references. This means that
		// any use of mDrawables cache must only occur when burn in protection is NOT active.
		// If turning off burn-in protection, recache regular watch face drawables.
		if (mBurnInProtectionChangedSinceLastDraw) {
			mBurnInProtectionChangedSinceLastDraw = false;
			setLayout(mIsBurnInProtection ? Rez.Layouts.AlwaysOn(dc) : Rez.Layouts.WatchFace(dc));
			cacheDrawables();
		}

		// Respond now to any settings change since last full draw, as we can now update the full screen.
		if (mSettingsChangedSinceLastDraw) {
			onSettingsChangedSinceLastDraw();
		}

		// Clear any partial update clipping.
		if (dc has :clearClip) {
			dc.clearClip();
		}

		updateGoalMeters();

		// Call the parent onUpdate function to redraw the layout
		View.onUpdate(dc);
	}

	// Update each goal meter separately, then also pass types and values to data area to draw goal icons.
	function updateGoalMeters() {
		if (mIsBurnInProtection) {
			return;
		}

		var leftType = getPropertyValue("LeftGoalType");
		var leftValues = getValuesForGoalType(leftType);
		(mDrawables[:LeftGoalMeter] as GoalMeter).setValues(leftValues[:current], leftValues[:max], /* isOff */ leftType == GOAL_TYPE_OFF);

		var rightType = getPropertyValue("RightGoalType");
		var rightValues = getValuesForGoalType(rightType);
		(mDrawables[:RightGoalMeter] as GoalMeter).setValues(rightValues[:current], rightValues[:max], /* isOff */ rightType == GOAL_TYPE_OFF);

		(mDrawables[:DataArea] as DataArea).setGoalValues(leftType, leftValues, rightType, rightValues);
	}

	function getValuesForGoalType(type) as GoalValues  {
		var values = {
			:current => 0,
			:max => 1,
			:isValid => true
		};

		var info = ActivityMonitor.getInfo();

		switch(type) {
			case GOAL_TYPE_STEPS:
				values[:current] = info.steps;
				values[:max] = info.stepGoal;
				break;

			case GOAL_TYPE_FLOORS_CLIMBED:
				if (info has :floorsClimbed) {
					values[:current] = info.floorsClimbed;
					values[:max] = info.floorsClimbedGoal;
				} else {
					values[:isValid] = false;
				}

				break;

			case GOAL_TYPE_ACTIVE_MINUTES:
				if (info has :activeMinutesWeek) {
					values[:current] = info.activeMinutesWeek.total;
					values[:max] = info.activeMinutesWeekGoal;
				} else {
					values[:isValid] = false;
				}
				break;

			case GOAL_TYPE_BATTERY:
				// #8: floor() battery to be consistent.
				values[:current] = Math.floor(Sys.getSystemStats().battery).toNumber();
				values[:max] = 100;
				break;

			case GOAL_TYPE_CALORIES:
				values[:current] = info.calories;

				// #123 Protect against null value returned by getProperty(). Trigger invalid goal handling code below.
				// Protect against unexpected type e.g. String.
				values[:max] = App.getApp().getIntProperty("CaloriesGoal", 2000);
				break;

			case GOAL_TYPE_OFF:
				values[:isValid] = false;
				break;
		}

		// #16: If user has set goal to zero, or negative (in simulator), show as invalid. Set max to 1 to avoid divide-by-zero
		// crash in GoalMeter.getSegmentScale().
		if (values[:max] < 1) {
			values[:max] = 1;
			values[:isValid] = false;
		}

		return values;
	}

	// Set clipping region to previously-displayed seconds text only.
	// Clear background, clear clipping region, then draw new seconds.
	function onPartialUpdate(dc) {
		//Sys.println("onPartialUpdate()");

		mDataFields.update(dc, /* isPartialUpdate */ true);
		mTime.drawSeconds(dc, /* isPartialUpdate */ true);
	}

	/*
	// Called when this View is removed from the screen. Save the
	// state of this View here. This includes freeing resources from
	// memory.
	function onHide() {
	}
	*/

	// The user has just looked at their watch. Timers and animations may be started here.
	function onExitSleep() {
		mIsSleeping = false;

		//Sys.println("onExitSleep()");

		// If watch does not support per-second updates, AND HideSeconds property is false,
		// show seconds, and make move bar original width.
		if (!PER_SECOND_UPDATES_SUPPORTED && !getPropertyValue("HideSeconds")) {
			setHideSeconds(false);
		}

		// Rather than checking the need for background requests on a timer, or on the hour, easier just to check when exiting
		// sleep.
		if (MadreApp has :checkPendingWebRequests) { // checkPendingWebRequests() can be excluded to save memory.
			App.getApp().checkPendingWebRequests();
		}

		// If watch requires burn-in protection, set flag to false when entering sleep.
		var settings = Sys.getDeviceSettings();
		if (settings has :requiresBurnInProtection && settings.requiresBurnInProtection) {
			mIsBurnInProtection = false;
			mBurnInProtectionChangedSinceLastDraw = true;
		}
	}

	// Terminate any active timers and prepare for slow updates.
	function onEnterSleep() {
		mIsSleeping = true;

		//Sys.println("onEnterSleep()");
		//Sys.println("Partial updates supported = " + PER_SECOND_UPDATES_SUPPORTED);

		// If watch does not support per-second updates, then hide seconds, and make move bar full width.
		// onUpdate() is about to be called one final time before entering sleep.
		// If HideSeconds property is true, do not wastefully hide seconds again (they should already be hidden).
		if (!PER_SECOND_UPDATES_SUPPORTED && !getPropertyValue("HideSeconds")) {
			setHideSeconds(true);
		}

		// If watch requires burn-in protection, set flag to true when entering sleep.
		var settings = Sys.getDeviceSettings();
		if (settings has :requiresBurnInProtection && settings.requiresBurnInProtection) {
			mIsBurnInProtection = true;
			mBurnInProtectionChangedSinceLastDraw = true;
		}

		Ui.requestUpdate();
	}

	function isSleeping() {
		return mIsSleeping;
	}

	function setHideSeconds(hideSeconds) {

		// #158 Venu 2.80 firmware crash: mIsBurnInProtection fails to be set in onEnterSleep(), hopefully because that function
		// is now not called at startup before entering sleep, rather than because requiresBurnInProtection is not set. mTime will
		// be null in always-on mode, so add additional safety check here.
		// TODO: If Venu is guaranteed to start in always-on mode, we could initialise mIsBurnInProtection to true if
		// requiresBurnInProtection is true.
		if (mIsBurnInProtection || (mTime == null)) {
			return;
		}

		mTime.setHideSeconds(hideSeconds);
		(mDrawables[:MoveBar] as MoveBar).setFullWidth(hideSeconds);
	}
}

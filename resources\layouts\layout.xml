<resources>
	<layout id="WatchFace">
		<drawable class="Background" />

		<!-- Instinct-style layout: Clean and focused design -->

		<!-- Day and Date at top - Instinct style -->
		<drawable id="Date" class="DateLine">
			<param name="y">25</param>
		</drawable>

		<!-- Large time display in center - Instinct style positioning -->
		<drawable id="Time" class="ThickThinTime">
			<param name="secondsX">185</param>
			<param name="secondsY">85</param>
			<param name="secondsClipY">77</param>
			<param name="secondsClipWidth">35</param>
			<param name="secondsClipHeight">25</param>
			<param name="adjustY">-20</param>
		</drawable>

		<!-- Battery indicator in top right corner - Instinct style -->
		<drawable id="Indicators" class="Indicators">
			<param name="locX">200</param>
			<param name="locY">15</param>
			<param name="spacingY">12</param>
			<param name="batteryWidth">30</param>
		</drawable>

		<!-- Large data fields positioned like Instinct - bigger numbers, more separated -->
		<drawable id="DataFields" class="DataFields">
			<param name="left">20</param>
			<param name="right">220</param>
			<param name="top">125</param>
			<param name="bottom">160</param>
			<param name="batteryWidth">28</param>
		</drawable>

		<!-- Bottom data area for additional info - Instinct style -->
		<drawable id="DataArea" class="DataArea">
			<param name="locX">50</param>
			<param name="width">140</param>
			<param name="row1Y">180</param>
			<param name="row2Y">200</param>
			<param name="goalIconY">175</param>
			<param name="goalIconLeftX">35</param>
			<param name="goalIconRightX">205</param>
		</drawable>

		<!-- Minimal goal meters - very thin like Instinct -->
		<drawable id="LeftGoalMeter" class="GoalMeter">
			<param name="side">:left</param>
			<param name="stroke">3</param>
			<param name="height">100</param>
			<param name="separator">1</param>
		</drawable>

		<drawable id="RightGoalMeter" class="GoalMeter">
			<param name="side">:right</param>
			<param name="stroke">3</param>
			<param name="height">100</param>
			<param name="separator">1</param>
		</drawable>

		<!-- Move bar at bottom - Instinct style -->
		<drawable id="MoveBar" class="MoveBar">
			<param name="x">25</param>
			<param name="y">220</param>
			<param name="width">190</param>
			<param name="height">5</param>
			<param name="separator">1</param>
		</drawable>

	</layout>

	<!-- Only used for OLED watches, but Rez.Layouts.AlwaysOn symbol must always exist to prevent compiler errors -->
	<layout id="AlwaysOn">
	</layout>
</resources>